Raw hero response from DB: {
  count: 15,
  data: [
    {
      anilist_id: 175914,
      romaji_title: 'Yofukashi no Uta Season 2',
      english_title: 'Call of the Night Season 2',
      cover_image: 'https://pixeldrain.com/api/file/hZx6bq4r',
      banner_image: 'https://pixeldrain.com/api/file/84aA5GnF',
      description: '<PERSON><PERSON> no <PERSON>ta (Call of the Night).\n' +
        '<br>\n' +
        'Kou przezwycięża swoje wątpliwości co do zostania wampirem i postanawia „polubić” Nazunę, a Nazuna z kolei postanawia sprawić, by <PERSON><PERSON> „zakoch<PERSON><PERSON> się” w niej. Oboje, nie do końca rozumi<PERSON>, czym właściwie jest „miłość”, spędzają kolejne noce w szaleńczym rytmie. Tymczasem detektyw Anko Ugu<PERSON>u zbliża się coraz bardziej, real<PERSON><PERSON><PERSON><PERSON><PERSON> swój plan eksterminacji wampirów.\n',
      average_score: 8.1,
      popularity: 40897,
      trending: 210,
      format: 'TV',
      season: 'SUMMER',
      season_year: 2025,
      genres: [Array],
      total_episodes: null,
      released_episodes: 1,
      latest_episode: [Object],
      rankings: [Object],
      status: 'RELEASING',
      next_airing_episode: [Object],
      episodes: [Array]
    },
    {
      anilist_id: 185407,
      romaji_title: 'Takopii no Genzai',
      english_title: "Takopi's Original Sin",
      cover_image: 'https://pixeldrain.com/api/file/2G9mrLbX',
      banner_image: null,
      description: 'Szczęśliwy kosmita Takopi ląduje na Ziemi z jednym zadaniem: szerzyć radość! Spotykając samotną czwartoklasistkę Shizukę, obiecuje przywrócić jej uśmiech za pomocą swoich magicznych Happy Gadżetów. Jednak gdy odkrywa ból, który kryje się w jej życiu, Takopi uczy się, że prawdziwe szczęście wymaga czegoś więcej niż tylko gadżetów.',
      average_score: 8.5,
      popularity: 25178,
      trending: 185,
      format: 'ONA',
      season: 'SUMMER',
      season_year: 2025,
      genres: [Array],
      total_episodes: 6,
      released_episodes: 2,
      latest_episode: [Object],
      rankings: [Object],
      status: 'RELEASING',
      next_airing_episode: [Object],
      episodes: [Array]
    },
    {
      anilist_id: 186052,
      romaji_title: 'Mizu Zokusei no Mahou Tsukai',
      english_title: 'The Water Magician',
      cover_image: 'https://pixeldrain.com/api/file/wdka7MiN',
      banner_image: null,
      description: 'Ryou z radością odkrywa, że odrodził się w fantastycznym świecie Phi i myśli, że wreszcie będzie mógł prowadzić spokojne życie, ucząc się magii wody. Jednak „płynąć z prądem” w tym świecie nabiera zupełnie innego znaczenia. Ryou od razu trafia na dzikie tereny i musi zmierzyć się z groźnymi potworami.\n' +
        '<br>\n' +
        'Choć jego codzienność to walka o przetrwanie, Ryou nie porzuca marzeń o spokojnym życiu... Jest optymistą, sprytnym chłopakiem i do tego posiada ukrytą cechę „Wiecznej Młodości”. Mija dwadzieścia lat, a każde spotkanie i każda potyczka zbliżają go do szczytów ludzkiej magii. Nawet nie podejrzewa, że to dopiero początek jego historii. Pewne przeznaczone spotkanie wkrótce stawia go w centrum wydarzeń, które odmienią bieg wydarzeń...\n' +
        '<br>\n' +
        'Tak zaczyna się przygoda najsilniejszego maga wody, jakiego widział świat... który przy okazji lubi wszystko robić we własnym tempie!\n',
      average_score: 6.5,
      popularity: 17463,
      trending: 121,
      format: 'TV',
      season: 'SUMMER',
      season_year: 2025,
      genres: [Array],
      total_episodes: null,
      released_episodes: 1,
      latest_episode: [Object],
      rankings: [Object],
      status: 'RELEASING',
      next_airing_episode: [Object],
      episodes: [Array]
    },
    {
      anilist_id: 169420,
      romaji_title: 'Watari-kun no xx ga Houkai Sunzen',
      english_title: "Watari-kun's ****** Is about to Collapse",
      cover_image: 'https://pixeldrain.com/api/file/cjmfgCtU',
      banner_image: null,
      description: 'Naoto Watari żyje wyłącznie dla swojej młodszej siostry, Suzushiro. Jego uporządkowany świat wywraca się jednak do góry nogami, gdy w jego życie ponownie wkracza Satsuki - hałaśliwa przyjaciółka z dzieciństwa. Choć nie wypowiada ani słowa, sama jej obecność wywołuje u Naoto lawinę wspomnień i burzy jego codzienną rutynę.\n' +
        '<br>\n' +
        'W miarę jak napięcie rośnie, a skrywane sekrety wychodzą na jaw, oddanie Naoto wobec Suzushiro zderza się z dawnym bólem, 
grożąc rozpadem jego kruchego świata.\n',
      average_score: 6,
      popularity: 8857,
      trending: 87,
      format: 'TV',
      season: 'SUMMER',
      season_year: 2025,
      genres: [Array],
      total_episodes: null,
      released_episodes: 1,
      latest_episode: [Object],
      rankings: [Object],
      status: 'RELEASING',
      next_airing_episode: [Object],
      episodes: [Array]
    },
    {
      anilist_id: 185736,
      romaji_title: 'Vigilante: Boku no Hero Academia ILLEGALS',
      english_title: 'My Hero Academia: Vigilantes',
      cover_image: 'https://pixeldrain.com/api/file/fH7gfza4',
      banner_image: 'https://pixeldrain.com/api/file/pf29T42x',
      description: 'W świecie My Hero Academia nie każdy potrzebuje licencji, by walczyć o sprawiedliwość!\r\n' +
        '<br>\r\n' +
        'W społeczeństwie pełnym supermocy, zło stało się czymś, co nie ma nic wspólnego z „normalnością”. Bohaterowie, przeszkoleni i posiadający licencję do ochrony i obrony społeczeństwa przed superzłoczyńcami, stoją ponad wszystkimi. Nie każdy jednak może zostać bohaterem, a są tacy, którzy chcieliby wykorzystać swoje moce, by służyć ludziom, mimo braku legalnego zezwolenia. Ale czy walczą o sprawiedliwość w cieniu, czy z powodów, które znają tylko oni sami? Cokolwiek by to nie było, nazywani są… Vigilante.\r\n' + 
        '<br>\r\n' +
        'Koichi Haimawari nie przeszedł selekcji, by zostać oficjalnym bohaterem, więc wykorzystuje swoją skromną moc, by czynić dobre uczynki w wolnym czasie. Pewnego dnia, po fatalnym spotkaniu z lokalnymi bandytami, łączy siły z dwoma innymi osobami. Żaden z 
nich tak naprawdę nie wie, co robi, ale mają odwagę – albo głupotę – by spróbować. Szybko odkrywają, że walka ze złem wymaga czegoś więcej niż tylko odwagi…\r\n',
      average_score: 7.6,
      popularity: 45602,
      trending: 56,
      format: 'TV',
      season: 'SPRING',
      season_year: 2025,
      genres: [Array],
      total_episodes: 13,
      released_episodes: 13,
      latest_episode: [Object],
      rankings: [Object],
      status: 'FINISHED',
      next_airing_episode: [Object],
      episodes: [Array]
    },
    {
      anilist_id: 184639,
      romaji_title: 'Anne Shirley',
      english_title: 'Anne Shirley',
      cover_image: 'https://pixeldrain.com/api/file/iGgwasgH',
      banner_image: 'https://pixeldrain.com/api/file/RYru8wGH',
      description: 'Gdy osierocona Ania zostaje adoptowana przez Marylę i Mateusza Cuthbertów, jej niezwykła wyobraźnia i piękne serce, ukształtowane przez jedenaście lat trudnego życia, odmieniają serca ludzi wokół niej. To opowieść pełna miłości, ukazująca małe radości życia poprzez ludzkie emocje i piękno natury.\r\n',
      average_score: 7.4,
      popularity: 15649,
      trending: 60,
      format: 'TV',
      season: 'SPRING',
      season_year: 2025,
      genres: [Array],
      total_episodes: 24,
      released_episodes: 13,
      latest_episode: [Object],
      rankings: [Object],
      status: 'RELEASING',
      next_airing_episode: [Object],
      episodes: [Array]
    },
    {
      anilist_id: 180516,
      romaji_title: 'Uma Musume: Cinderella Gray',
      english_title: 'Uma Musume: Cinderella Gray',
      cover_image: 'https://pixeldrain.com/api/file/LxyLugib',
      banner_image: 'https://pixeldrain.com/api/file/UA5dLii6',
      description: 'Uma Musume Cinderella Gray to spin-off serii Uma Musume. Historia podąża za Oguri Cap podczas jej nauki w Akademii Treningowej Kasamatsu oraz na drodze do stania się legendarną końską dziewczyną.\r\n',
      average_score: 8.4,
      popularity: 15971,
      trending: 21,
      format: 'TV',
      season: 'SPRING',
      season_year: 2025,
      genres: [Array],
      total_episodes: 13,
      released_episodes: 13,
      latest_episode: [Object],
      rankings: [Object],
      status: 'FINISHED',
      next_airing_episode: [Object],
      episodes: [Array]
    },
    {
      anilist_id: 143200,
      romaji_title: 'Summer Pockets',
      english_title: 'Summer Pockets',
      cover_image: 'https://pixeldrain.com/api/file/ieo9pGbN',
      banner_image: 'https://pixeldrain.com/api/file/SarpXyxW',
      description: 'Aby uporządkować rzeczy po swojej niedawno zmarłej babci, główny bohater, Takahara Hairi, udaje się na wyspę Torishirojima podczas wakacji letnich.\r\n' +
        '<br>\r\n' +
        'Po wyjściu z promu dostrzega samotną dziewczynę stojącą przy brzegu. Dziewczynę, która po prostu patrzy w dal, a jej długie włosy powiewają na wietrze. Patrzy na nią w całkowitym zdumieniu, ale zanim się zorientuje, dziewczyna znika, a on nie może jej już nigdzie znaleźć.\r\n' +
        '<br>\r\n' +
        'Hairi zaczyna przyzwyczajać się do życia na tej wyspie, otoczonej przyrodą o wiele bardziej bujną niż wszystko, co znał w 
mieście. Czas płynie tu spokojnie, a wraz z nim wracają wspomnienia czegoś nostalgicznego... Wspomnienia czegoś, co dawno zapomniał.\r\n',
      average_score: 6.9,
      popularity: 23742,
      trending: 16,
      format: 'TV',
      season: 'SPRING',
      season_year: 2025,
      genres: [Array],
      total_episodes: 26,
      released_episodes: 13,
      latest_episode: [Object],
      rankings: [Object],
      status: 'RELEASING',
      next_airing_episode: [Object],
      episodes: [Array]
    },
    {
      anilist_id: 177476,
      romaji_title: 'Shin Samurai-den YAIBA',
      english_title: 'YAIBA: Samurai Legend',
      cover_image: 'https://pixeldrain.com/api/file/G7QSunHL',
      banner_image: 'https://pixeldrain.com/api/file/5Qrkz6Tn',
      description: 'Yaiba Kurogane spędził lata na treningu w dżungli, dążąc do swojego celu – zostania prawdziwym samurajem. Los sprawia, że wraca do Japonii i zamieszkuje z rodziną Mine, która ma powiązania z jego ojcem, Kenjurou. Yaiba nieustannie zadziwia Sayakę, córkę Mine, swoim dzikim i lekkomyślnym zachowaniem.\r\n' +
        '<br>\r\n' +
        'Pewnego dnia Yaiba towarzyszy Sayace do szkoły i napotyka na swojej drodze Takeshiego Onimaru, mistrza kendo. Ich starcia 
stają się coraz częstsze, aż w odpowiedzi na ich poszukiwanie siły budzą się dwie pradawne moce: Fujinken – Miecz Boga Wiatru i Raijinken – Miecz Boga Piorunów. Oba te nadprzyrodzone ostrza, które od wieków wstrząsały światem, ponownie się przebudziły… a prawda 
o nich wreszcie zostaje ujawniona!\r\n',
      average_score: 7.1,
      popularity: 16971,
      trending: 8,
      format: 'TV',
      season: 'SPRING',
      season_year: 2025,
      genres: [Array],
      total_episodes: null,
      released_episodes: 13,
      latest_episode: [Object],
      rankings: [Object],
      status: 'RELEASING',
      next_airing_episode: [Object],
      episodes: [Array]
    },
    {
      anilist_id: 169440,
      romaji_title: 'Jidou Hanbaiki ni Umarekawatta Ore wa Meikyuu wo Samayou 2nd Season',
      english_title: 'Reborn as a Vending Machine, I Now Wander the Dungeon Season 2',
      cover_image: 'https://pixeldrain.com/api/file/8dZsdn6s',
      banner_image: null,
      description: 'Drugi sezon Jidou Hanbaiki ni Umarekawatta Ore wa Meikyuu wo Samayou.\n',
      average_score: 6.3,
      popularity: 12098,
      trending: 25,
      format: 'TV',
      season: 'SUMMER',
      season_year: 2025,
      genres: [Array],
      total_episodes: null,
      released_episodes: 1,
      latest_episode: [Object],
      rankings: [Object],
      status: 'RELEASING',
      next_airing_episode: [Object],
      episodes: [Array]
    },
    {
      anilist_id: 179344,
      romaji_title: 'Kanojo, Okarishimasu 4th Season',
      english_title: 'Rent-a-Girlfriend Season 4',
      cover_image: 'https://pixeldrain.com/api/file/qf95Qy1h',
      banner_image: null,
      description: 'Czwarty sezon Kanojo, Okarishimasu (Rent-a-Girlfriend).\n' +
        '<br>\n' +
        'Kazuya jest gotów wyznać swoje uczucia, a tłem dla jego marzenia staje się rajska sceneria Hawajów. Jednak wynajęcie Chizuru na tę wymarzoną podróż szybko przeradza się w pole bitwy miłosnych zawirowań — wszystko przez natarczywe zaloty Ruki i intrygi Mami.\n',
      average_score: 6.2,
      popularity: 18706,
      trending: 29,
      format: 'ONA',
      season: 'SUMMER',
      season_year: 2025,
      genres: [Array],
      total_episodes: null,
      released_episodes: 1,
      latest_episode: [Object],
      rankings: [Object],
      status: 'RELEASING',
      next_airing_episode: [Object],
      episodes: [Array]
    },
    {
      anilist_id: 180460,
      romaji_title: 'Mattaku Saikin no Tantei to Kitara',
      english_title: 'Detectives These Days Are Crazy!',
      cover_image: 'https://pixeldrain.com/api/file/FzXbf4ns',
      banner_image: 'https://pixeldrain.com/api/file/RW6qfgCQ',
      description: 'Keiichirō Nagumo, niegdyś legendarny detektyw, dziś zagubiony w świecie smartfonów i nowoczesności, powoli popada w zapomnienie. Jego spokojny, rutynowy żywot zostaje przerwany, gdy do jego biura wpada Mashiro - zadziorna licealistka, która uparcie chce zostać jego uczennicą. Choć Keiichirō jest przyzwyczajony do dawnych metod, jej nieposkromiony entuzjazm wciąga go z powrotem w świat śledztw. Tak rozpoczyna się przygoda nietypowego duetu detektywów, których dzieli pokolenie, ale łączy pragnienie odkrywania prawdy!\n',
      average_score: 6.6,
      popularity: 8543,
      trending: 20,
      format: 'TV',
      season: 'SUMMER',
      season_year: 2025,
      genres: [Array],
      total_episodes: 12,
      released_episodes: 1,
      latest_episode: [Object],
      rankings: [Object],
      status: 'RELEASING',
      next_airing_episode: [Object],
      episodes: [Array]
    },
    {
      anilist_id: 170113,
      romaji_title: 'Koujo Denka no Katei Kyoushi',
      english_title: "Private Tutor to the Duke's Daughter",
      cover_image: 'https://pixeldrain.com/api/file/MbCej59W',
      banner_image: null,
      description: 'Po nieudanym egzaminie na nadwornego maga, Allen nie miał jak wrócić. Otrzymuje nagle niespodziewaną propozycję pracy. Zostaje prywatnym nauczycielem córki księcia. Gdy zaczyna już nieco opuszczać gardę, odkrywa, że dziewczynka… w ogóle nie potrafi korzystać z magii!\n' +
        '<br>\n' +
        'Co blokuje jej zdolności? Dzięki nieszablonowym metodom nauczania Allena, przyszłość jego nietypowej uczennicy powoli zaczyna się rozjaśniać.\n',
      average_score: 6.5,
      popularity: 8291,
      trending: 9,
      format: 'ONA',
      season: 'SUMMER',
      season_year: 2025,
      genres: [Array],
      total_episodes: null,
      released_episodes: 1,
      latest_episode: [Object],
      rankings: [Object],
      status: 'RELEASING',
      next_airing_episode: [Object],
      episodes: [Array]
    },
    {
      anilist_id: 182419,
      romaji_title: 'Chuuzenji-sensei Mononoke Kougiroku: Sensei ga Nazo wo Toiteshimau kara.',
      english_title: 'The Mononoke Lecture Logs of Chuzenji-sensei: He Just Solves All the Mysteries',
      cover_image: 'https://pixeldrain.com/api/file/AXR9jE9a',
      banner_image: 'https://pixeldrain.com/api/file/c7VxAeWN',
      description: 'Jesteśmy w Tokio w 1948 roku, tuż po wojnie. Kanna Kusakabe właśnie rozpoczęła drugi rok nauki w liceum, gdy poznaje nowego nauczyciela języka – Akihiko Chūzenjiego.\r\n' +
        '<br>\r\n' +
        'Wokół Kanny nieustannie dochodzi do tajemniczych, nadprzyrodzonych zjawisk. I tak jak każdego dnia, dziś również otworzy drzwi do pokoju przygotowawczego w bibliotece, by szukać pomocy u gburowatego Chūzenjiego-sensei, który czeka w środku.\r\n' +      
        '<br>\r\n' +
        'Rozpoczyna się licealna opowieść detektywistyczna, w której nieoczekiwany duet – nauczyciel i uczennica – zmierzy się z tajemnicami nie z tego świata!',
      average_score: 6.1,
      popularity: 4941,
      trending: 3,
      format: 'TV',
      season: 'SPRING',
      season_year: 2025,
      genres: [Array],
      total_episodes: 12,
      released_episodes: 11,
      latest_episode: [Object],
      rankings: [Object],
      status: 'FINISHED',
      next_airing_episode: [Object],
      episodes: [Array]
    },
    {
      anilist_id: 178825,
      romaji_title: 'CLASSIC★STARS',
      english_title: 'CLASSIC★STARS',
      cover_image: 'https://pixeldrain.com/api/file/SWqQnF9n',
      banner_image: null,
      description: 'Anime CLASSIC★STARS rozgrywa się w Prywatnej Akademii Gloria, gdzie gromadzą się wschodzące gwiazdy muzyki, sztuki i sportu. W wydziale muzycznym tej szkoły wszczepia się „Dary” słynnych muzyków z przeszłości w ciała uczniów, którzy dorównują im talentem. Ci wybrani uczniowie przyjmują następnie imiona dawnych mistrzów.\r\n' +
        '<br>\r\n' +
        'Historia skupia się na Beethovenie – chłopcu, który okazuje się kompatybilny z „Darem” Ludwiga van Beethovena i zostaje przyjęty do prestiżowej akademii.',
      average_score: 5,
      popularity: 2088,
      trending: 1,
      format: 'TV',
      season: 'SPRING',
      season_year: 2025,
      genres: [Array],
      total_episodes: 13,
      released_episodes: 13,
      latest_episode: [Object],
      rankings: [Object],
      status: 'FINISHED',
      next_airing_episode: [Object],
      episodes: [Array]
    }
  ]
}
Processed hero items: {
  count: 15,
  items: [
    {
      id: 175914,
      title: 'Yofukashi no Uta Season 2',
      description: 'has description',
      latest_episode: [Object],
      rating: 8.1
    },
    {
      id: 185407,
      title: 'Takopii no Genzai',
      description: 'has description',
      latest_episode: [Object],
      rating: 8.5
    },
    {
      id: 186052,
      title: 'Mizu Zokusei no Mahou Tsukai',
      description: 'has description',
      latest_episode: [Object],
      rating: 6.5
    },
    {
      id: 169420,
      title: 'Watari-kun no xx ga Houkai Sunzen',
      description: 'has description',
      latest_episode: [Object],
      rating: 6
    },
    {
      id: 185736,
      title: 'Vigilante: Boku no Hero Academia ILLEGALS',
      description: 'has description',
      latest_episode: [Object],
      rating: 7.6
    },
    {
      id: 184639,
      title: 'Anne Shirley',
      description: 'has description',
      latest_episode: [Object],
      rating: 7.4
    },
    {
      id: 180516,
      title: 'Uma Musume: Cinderella Gray',
      description: 'has description',
      latest_episode: [Object],
      rating: 8.4
    },
    {
      id: 143200,
      title: 'Summer Pockets',
      description: 'has description',
      latest_episode: [Object],
      rating: 6.9
    },
    {
      id: 177476,
      title: 'Shin Samurai-den YAIBA',
      description: 'has description',
      latest_episode: [Object],
      rating: 7.1
    },
    {
      id: 169440,
      title: 'Jidou Hanbaiki ni Umarekawatta Ore wa Meikyuu wo Samayou 2nd Season',
      description: 'has description',
      latest_episode: [Object],
      rating: 6.3
    },
    {
      id: 179344,
      title: 'Kanojo, Okarishimasu 4th Season',
      description: 'has description',
      latest_episode: [Object],
      rating: 6.2
    },
    {
      id: 180460,
      title: 'Mattaku Saikin no Tantei to Kitara',
      description: 'has description',
      latest_episode: [Object],
      rating: 6.6
    },
    {
      id: 170113,
      title: 'Koujo Denka no Katei Kyoushi',
      description: 'has description',
      latest_episode: [Object],
      rating: 6.5
    },
    {
      id: 182419,
      title: 'Chuuzenji-sensei Mononoke Kougiroku: Sensei ga Nazo wo Toiteshimau kara.',
      description: 'has description',
      latest_episode: [Object],
      rating: 6.1
    },
    {
      id: 178825,
      title: 'CLASSIC★STARS',
      description: 'has description',
      latest_episode: [Object],
      rating: 5
    }
  ]
}
Processing hero data - input: {
  count: 15,
  sample: [
    {
      id: 175914,
      title: 'Yofukashi no Uta Season 2',
      rating: 8.1,
      trending: 210,
      popularity: 40897,
      latest_episode: [Object],
      rankings: [Object]
    },
    {
      id: 185407,
      title: 'Takopii no Genzai',
      rating: 8.5,
      trending: 185,
      popularity: 25178,
      latest_episode: [Object],
      rankings: [Object]
    },
    {
      id: 186052,
      title: 'Mizu Zokusei no Mahou Tsukai',
      rating: 6.5,
      trending: 121,
      popularity: 17463,
      latest_episode: [Object],
      rankings: [Object]
    }
  ]
}
Hero categories: {
  recentHighQuality: 0,
  topRanked: 0,
  trending: 0,
  hidden_gems: 0,
  regular: 15
}
Remaining pool for hero selection: { count: 0, currentSelectionCount: 1, sample: [] }
Final hero selection before shuffle: {
  count: 1,
  items: [
    {
      id: 169440,
      title: 'Jidou Hanbaiki ni Umarekawatta Ore wa Meikyuu wo Samayou 2nd Season',
      heroReason: 'Z ostatnich 7 dni'
    }
  ]
}
Final processed hero data: {
  count: 1,
  items: [
    {
      id: 169440,
      title: 'Jidou Hanbaiki ni Umarekawatta Ore wa Meikyuu wo Samayou 2nd Season',
      heroReason: 'Z ostatnich 7 dni'
    }
  ]
}