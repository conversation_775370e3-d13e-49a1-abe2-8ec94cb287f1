//src/routes/api/home/<USER>
import { json } from '@sveltejs/kit';
import { formatDistanceToNow } from 'date-fns';
import { pl } from 'date-fns/locale';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

function processHeroData(heroItems) {
  console.log('Processing hero data - input:', {
    count: heroItems.length,
    sample: heroItems.slice(0, 3).map(item => ({
      id: item.anilist_id,
      title: item.romaji_title,
      rating: item.average_score,
      trending: item.trending,
      popularity: item.popularity,
      latest_episode: item.latest_episode,
      rankings: item.rankings
    }))
  });

  const categories = {
    recentHighQuality: heroItems.filter(show => {
      const releaseDate = new Date(show.latest_episode.airDate);
      const isRecent = Date.now() - releaseDate <= 2 * 24 * 60 * 60 * 1000;
      return isRecent && show.rating >= 7.5;
    }),
    topRanked: heroItems.filter(show => {
      // Check for seasonal ranking using the raw rankings array
      if (show.rankings?.raw && Array.isArray(show.rankings.raw)) {
        const seasonalRanking = show.rankings.raw.find(r =>
          r.type === 'POPULAR' &&
          r.allTime === false &&
          r.season !== null &&
          r.context === 'most popular');
        return seasonalRanking && seasonalRanking.rank <= 5 && show.rating >= 8.0;
      }
      return false;
    }),
    trending: heroItems.filter(show =>
      show.trending > 500 &&
      show.rating >= 7.0
    ),
    //not currently used
    hidden_gems: heroItems.filter(show =>
      show.rating >= 10 &&
      show.popularity < 800 &&
      show.trending < 300
    ),
    regular: heroItems.filter(show =>
      show.latest_episode.airDate &&
      new Date(show.latest_episode.airDate) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    )
  };

  console.log('Hero categories:', {
    recentHighQuality: categories.recentHighQuality.length,
    topRanked: categories.topRanked.length,
    trending: categories.trending.length,
    hidden_gems: categories.hidden_gems.length,
    regular: categories.regular.length
  });

  const finalSelection = [];

  if (categories.recentHighQuality.length) {
    const randomIndex = Math.floor(Math.random() * categories.recentHighQuality.length);
    finalSelection.push({
      ...categories.recentHighQuality[randomIndex],
      heroReason: 'Nowy Wysoko Oceniany'
    });
  }

  if (categories.topRanked.length) {
    // Filter out shows that are already in finalSelection
    const availableShows = categories.topRanked.filter(s =>
      !finalSelection.some(f => f.id === s.id)
    );

    if (availableShows.length) {
      const randomIndex = Math.floor(Math.random() * availableShows.length);
      const show = availableShows[randomIndex];

      // Get the seasonal ranking
      const seasonalRanking = show.rankings?.raw?.find(r =>
        r.type === 'POPULAR' &&
        r.allTime === false &&
        r.season !== null &&
        r.context === 'most popular');

      finalSelection.push({
        ...show,
        heroReason: seasonalRanking ? `#${seasonalRanking.rank} W tym sezonie` : 'Wysoka Ocena'
      });
    }
  }

  if (categories.trending.length) {
    // Filter out shows that are already in finalSelection
    const availableShows = categories.trending.filter(s =>
      !finalSelection.some(f => f.id === s.id)
    );

    if (availableShows.length) {
      const randomIndex = Math.floor(Math.random() * availableShows.length);
      const show = availableShows[randomIndex];

      finalSelection.push({
        ...show,
        heroReason: 'Popularne'
      });
    }
  }

  if (categories.hidden_gems.length) {
    // Filter out shows that are already in finalSelection
    const availableShows = categories.hidden_gems.filter(s =>
      !finalSelection.some(f => f.id === s.id)
    );

    if (availableShows.length) {
      const randomIndex = Math.floor(Math.random() * availableShows.length);
      const show = availableShows[randomIndex];

      finalSelection.push({
        ...show,
        heroReason: 'Ukryty Klejnot'
      });
    }
  }

  if (categories.regular.length) {
    // Filter out shows that are already in finalSelection
    const availableShows = categories.regular.filter(show =>
      !finalSelection.some(f => f.id === show.id)
    );

    if (availableShows.length) {
      const randomIndex = Math.floor(Math.random() * availableShows.length);
      const show = availableShows[randomIndex];

      finalSelection.push({
        ...show,
        heroReason: 'Z ostatnich 7 dni'
      });
    }
  }

  const remainingPool = heroItems
    .filter(show =>
      !finalSelection.some(f => f.id === show.id) &&
      show.rating >= 1 &&
      new Date(show.latest_episode.airDate) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    )
    .sort((a, b) => b.rating - a.rating);

  console.log('Remaining pool for hero selection:', {
    count: remainingPool.length,
    currentSelectionCount: finalSelection.length,
    sample: remainingPool.slice(0, 5).map(item => ({
      id: item.anilist_id,
      title: item.romaji_title,
      rating: item.rating,
      latest_episode_date: item.latest_episode.airDate
    }))
  });

  while (finalSelection.length < 5 && remainingPool.length) {
    const randomIndex = Math.floor(Math.random() * Math.min(remainingPool.length, 5));
    const show = remainingPool.splice(randomIndex, 1)[0];

    // Get the seasonal ranking
    const seasonalRanking = show.rankings?.raw?.find(r =>
      r.type === 'POPULAR' &&
      r.allTime === false &&
      r.season !== null &&
      r.context === 'most popular');

    finalSelection.push({
      ...show,
      heroReason: seasonalRanking ? `#${seasonalRanking.rank} W tym sezonie` : 'Polecane'
    });
  }

  console.log('Final hero selection before shuffle:', {
    count: finalSelection.length,
    items: finalSelection.map(item => ({
      id: item.anilist_id,
      title: item.romaji_title,
      heroReason: item.heroReason
    }))
  });

  return finalSelection.sort(() => Math.random() - 0.5);
}

export async function GET({ locals }) {
  const currentDate = new Date();
  const currentYear = 2025;
  const currentSeason = 'SPRING';
  const isLoggedIn = !!locals.session;

  try {
    // Define the promises to be resolved
    const promises = [
      supabase.rpc('get_hero_data', {
        current_season: currentSeason,
        current_year: currentYear
      }),
      supabase.rpc('get_new_releases', {
        limit_count: 40
      }),
      supabase.rpc('get_popular_now', {
        current_season: currentSeason,
        current_year: currentYear
      }),
      supabase.rpc('get_upcoming_episodes', {
        timestamp_ms: Date.now()
      }),
      supabase.rpc('get_release_schedule', {
        start_timestamp: currentDate.setHours(0, 0, 0, 0),
        end_timestamp: currentDate.setHours(23, 59, 59, 999)
      })
    ];

    // Only add the fake watching data for non-logged in users
    if (!isLoggedIn) {
      promises.push(
        supabase.rpc('get_continue_watching_fake', {
          limit_count: 12
        })
      );
    }

    const responses = await Promise.all(promises);

    const [
      heroResponse,
      newReleasesResponse,
      popularNowResponse,
      upcomingEpisodesResponse,
      releaseScheduleResponse,
      continueWatchingResponse
    ] = responses;

    // Only check the first 5 responses for errors
    [heroResponse, newReleasesResponse, popularNowResponse,
      upcomingEpisodesResponse, releaseScheduleResponse].forEach(response => {
        if (response.error) throw response.error;
      });

    console.log('Raw hero response from DB:', {
      count: heroResponse.data?.length || 0,
      data: heroResponse.data
    });

    const heroItems = heroResponse.data.map(anime => ({
      ...anime,
      short_synopsis: anime.synopsis?.slice(0, 235) +
        (anime.synopsis?.length >= 235 ? '...' : '')
    }));

    console.log('Processed hero items:', {
      count: heroItems.length,
      items: heroItems.map(item => ({
        id: item.anilist_id,
        title: item.romaji_title,
        description: item.description ? 'has description' : 'NO DESCRIPTION',
        latest_episode: item.latest_episode,
        rating: item.average_score
      }))
    });

    const newReleasesData = newReleasesResponse.data.map(release => ({
      id: release.id,
      title: release.title,
      english_title: release.english_title,
      episode: release.episode_number,
      total_episodes: release.total_episodes,
      image: release.thumbnail,
      duration: release.air_date,
      year: release.season_year,
      type: release.format,
      preview: release.preview
    }));

    const processedHeroData = processHeroData(heroItems);
    console.log('Final processed hero data:', {
      count: processedHeroData.length,
      items: processedHeroData.map(item => ({
        id: item.anilist_id,
        title: item.romaji_title,
        heroReason: item.heroReason
      }))
    });

    return json({
      heroData: processedHeroData,
      newReleasesData,
      popularNowData: popularNowResponse.data,
      upcomingEpisodesData: upcomingEpisodesResponse.data,
      releaseScheduleData: releaseScheduleResponse.data,
      continueWatchingData: !isLoggedIn && continueWatchingResponse ? continueWatchingResponse.data : [],
      isLoggedIn
    });

  } catch (error) {
    console.error('Error in home API:', error);
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: error.message,
        heroData: [],
        newReleasesData: [],
        popularNowData: [],
        upcomingEpisodesData: [],
        releaseScheduleData: [],
        continueWatchingData: [],
        isLoggedIn: false
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}